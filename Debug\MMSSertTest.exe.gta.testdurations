<?xml version="1.0" encoding="utf-8"?>
<GtaTestDurations xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <Executable>F:\work\iec61850_tests\iec61850\MMSSertTest\Debug\MMSSertTest.exe</Executable>
  <TestDurations>
    <TestDuration Test="MMSTests.GetLogicalDeviceDirectoryNonExistent" Duration="2" />
    <TestDuration Test="MMSTests.ReadNonExistent" Duration="2" />
    <TestDuration Test="MMSTests.GetLogicalDeviceDirectoryErrorCode" Duration="1" />
    <TestDuration Test="MMSTests.WriteConstant" Duration="14" />
    <TestDuration Test="MMSTests.WriteSetting" Duration="195" />
    <TestDuration Test="MMSTests.WriteQuality" Duration="71" />
    <TestDuration Test="MMSTests.AlternateAccessSimple" Duration="4" />
    <TestDuration Test="MMSTests.AlternateAccessComplex" Duration="5" />
  </TestDurations>
</GtaTestDurations>