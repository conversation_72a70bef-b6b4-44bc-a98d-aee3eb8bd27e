#include "MMSConnEnv.h"

#include <iostream>
#include <string>

using namespace std;

const char* DEFAULT_IP = "***************";
//const char* DEFAULT_IP = "127.0.0.1";
static const char* g_host = nullptr;

IedConnection MMSConnEnv::_connection;

IedConnection MMSConnEnv::getConn()
{
    return _connection;
}

void MMSConnEnv::SetHost(const char* host)
{
    g_host = host;
}

void MMSConnEnv::SetUp()
{
    IedClientError error;

    // Если IP не был установлен через SetHost, используем значение по умолчанию
    if (g_host == nullptr) {
        g_host = DEFAULT_IP;
        cout << "Use --ip x.x.x.x to override the default IP " << DEFAULT_IP << endl;
    }

    _connection = IedConnection_create();

	cout << "Connecting " << g_host << "..." << endl;
	IedConnection_connect(_connection, &error, g_host, 102);
	if (error != IED_ERROR_OK)
	{
		cerr << "Failed to connect to " << g_host << "\n";
		exit(1);
	}
	cout << "Connected" << endl;
}

void MMSConnEnv::TearDown() 
{
    cout << "Disconnecting" << endl;
	IedConnection_close(_connection);
}