#include "MMSConnEnv.h"

#include <iostream>
#include <string>

#pragma warning(push)
#pragma warning(disable: 4996) // Отключаем предупреждение для getenv

using namespace std;

//const char* DEFAULT_IP = "***************";
const char* DEFAULT_IP = "127.0.0.1";

IedConnection MMSConnEnv::_connection;

IedConnection MMSConnEnv::getConn()
{
    return _connection;
}



void MMSConnEnv::SetUp()
{
    IedClientError error;

    // Проверяем переменную окружения для IP
    const char* host = getenv("IEC61850_TEST_IP");
    if (host == nullptr) {
        host = DEFAULT_IP;
        cout << "Set IEC61850_TEST_IP environment variable to override the default IP " << DEFAULT_IP << endl;
    } else {
        cout << "Using IP from environment variable: " << host << endl;
    }

    _connection = IedConnection_create();

	cout << "Connecting " << host << "..." << endl;
	IedConnection_connect(_connection, &error, host, 102);
	if (error != IED_ERROR_OK)
	{
		cerr << "Failed to connect to " << host << "\n";
		exit(1);
	}
	cout << "Connected" << endl;
}

void MMSConnEnv::TearDown()
{
    cout << "Disconnecting" << endl;
	IedConnection_close(_connection);
}

#pragma warning(pop) // Восстанавливаем предупреждения