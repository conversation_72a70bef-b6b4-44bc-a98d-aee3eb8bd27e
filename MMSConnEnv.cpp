#include "MMSConnEnv.h"


#include <iostream>

using namespace std;

const char* HOST = "***************";

IedConnection MMSConnEnv::_connection;

IedConnection MMSConnEnv::getConn() 
{ 
    return _connection; 
}

void MMSConnEnv::SetUp()
{
    IedClientError error;

    _connection = IedConnection_create();

	cout << "Connecting " << HOST << "..." << endl;
	IedConnection_connect(_connection, &error, HOST, 102);	
	if (error != IED_ERROR_OK)
	{
		cerr << "Failed to connect to " << HOST << "\n";
		exit(1);
	}    
	cout << "Connected" << endl;        
}

void MMSConnEnv::TearDown() 
{
    cout << "Disconnecting" << endl;
	IedConnection_close(_connection);
}