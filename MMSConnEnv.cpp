#include "MMSConnEnv.h"

#include <iostream>
#include <string>

#pragma warning(push)
#pragma warning(disable: 4996) // Отключаем предупреждение для getenv

using namespace std;

//const char* DEFAULT_IP = "***************";
const char* DEFAULT_IP = "127.0.0.1";
static const char* g_host = nullptr;

IedConnection MMSConnEnv::_connection;

IedConnection MMSConnEnv::getConn()
{
    return _connection;
}

void MMSConnEnv::SetHost(const char* host)
{
    g_host = host;
}

void MMSConnEnv::SetUp()
{
    IedClientError error;

    // Если IP не был установлен через SetHost, проверяем переменную окружения
    if (g_host == nullptr) {
        const char* env_ip = getenv("IEC61850_TEST_IP");
        if (env_ip != nullptr) {
            g_host = env_ip;
            cout << "Using IP from environment variable: " << g_host << endl;
        } else {
            g_host = DEFAULT_IP;
            cout << "Use --ip x.x.x.x or set IEC61850_TEST_IP environment variable to override the default IP " << DEFAULT_IP << endl;
        }
    }

    _connection = IedConnection_create();

	cout << "Connecting " << g_host << "..." << endl;
	IedConnection_connect(_connection, &error, g_host, 102);
	if (error != IED_ERROR_OK)
	{
		cerr << "Failed to connect to " << g_host << "\n";
		exit(1);
	}
	cout << "Connected" << endl;
}

void MMSConnEnv::TearDown()
{
    cout << "Disconnecting" << endl;
	IedConnection_close(_connection);
}

#pragma warning(pop) // Восстанавливаем предупреждения