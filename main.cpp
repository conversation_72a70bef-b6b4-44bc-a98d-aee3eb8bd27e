#include <gtest/gtest.h>
#include <iostream>
#include <string>
#include <windows.h>

#include "MMSConnEnv.h"
#include "MmsValWrap.h"
#include "iec61850_client.h"

using namespace std;

// Функция для приближенного сравнения чисел с плавающей точкой
// Аналог doctest::Approx с автоматическим определением погрешности
bool IsApprox(float actual, float expected) {
    if (expected == 0.0f) {
        return abs(actual) < 1e-6f; // Абсолютная погрешность для нуля
    }
    float relativeError = abs((actual - expected) / expected);
    return relativeError < 1e-5f; // Относительная погрешность 0.001%
}

// Макрос для более удобного использования, похожий на doctest::Approx
#define EXPECT_APPROX(actual, expected) EXPECT_TRUE(IsApprox(actual, expected))

// Тест: sDsN1ae GetLogicalDeviceDirectory
TEST(MMSTests, GetLogicalDeviceDirectoryNonExistent) {
    // GetDataSetDirectoryRequest
    MmsError mmsError;
    LinkedList names;
    MmsConnection mmsCon = IedConnection_getMmsConnection(MMSConnEnv::getConn());

    names = MmsConnection_readNamedVariableListDirectory(mmsCon, &mmsError,
        "TEMPLATECTR_L", "LLN0$XYZ", NULL);
    if(names != NULL) {
        LinkedList_destroy(names);
    }
    EXPECT_EQ(mmsError, MMS_ERROR_ACCESS_OBJECT_NON_EXISTENT);
}

// Тест: sSrvN1f read non existent
TEST(MMSTests, ReadNonExistent) {
    // "TEMPLATECTRL/LLN0.NamPlt.vendorr";
    MmsValWrap value;
    MmsError mmsError;
    MmsConnection mmsCon = IedConnection_getMmsConnection(MMSConnEnv::getConn());

    value = MmsConnection_readVariable(mmsCon, &mmsError,
            "TEMPLATECTRL", "LLN0$DC$NamPlt$vendo_r");
    EXPECT_EQ(mmsError, MMS_ERROR_NONE);
    EXPECT_TRUE(value.isNotNull());
    EXPECT_EQ(value.getType(), MMS_DATA_ACCESS_ERROR);
    // object-non-existent
    EXPECT_EQ(MmsValue_getDataAccessError(*value), 10);
}

// Тест: sSrvN1a GetLogicalDeviceDirectory error code
TEST(MMSTests, GetLogicalDeviceDirectoryErrorCode) {
    MmsError mmsError;
    LinkedList names;
    MmsConnection mmsCon = IedConnection_getMmsConnection(MMSConnEnv::getConn());
    names = MmsConnection_getDomainVariableNames(mmsCon, &mmsError, "TEMPLATECTR_L");
    if(names != NULL) {
        LinkedList_destroy(names);
    }
    EXPECT_EQ(mmsError, MMS_ERROR_ACCESS_OBJECT_NON_EXISTENT);
}

// Тест: sSrv6 write constant
TEST(MMSTests, WriteConstant) {
    IedClientError err;
    const char* objRef = "TEMPLATECTRL/LLN0.LocSta.ctlModel";

    MmsValWrap value;

    value = IedConnection_readObject(MMSConnEnv::getConn(), &err,
        objRef, IEC61850_FC_CF);
    EXPECT_TRUE(value.isNotNull());

    EXPECT_EQ(value.getType(), MMS_INTEGER);
    EXPECT_EQ(MmsValue_toInt32(*value), 0);

    value = MmsValue_newInteger(1);
    IedConnection_writeObject(MMSConnEnv::getConn(), &err, objRef, IEC61850_FC_CF, *value);
    EXPECT_EQ(err, IED_ERROR_ACCESS_DENIED);
}

// Тест: sSrv6 write setting [device] - не работает с эмулятором на PC
TEST(MMSTests, WriteSetting) {
    //TEMPLATEMEAS/MMXU1.TotW.rangeC.max.f
    const char* objRef = "TEMPLATEMEAS/MMXU1.A.phsA.rangeC.max.f";
    IedClientError err;

    MmsValWrap value;

    // Считываем старое значение
    value = IedConnection_readObject(MMSConnEnv::getConn(), &err, objRef, IEC61850_FC_CF);
    EXPECT_EQ(err, IED_ERROR_OK);
    EXPECT_TRUE(value.isNotNull());
    EXPECT_EQ(value.getType(), MMS_FLOAT);

    float oldVal = MmsValue_toFloat(*value);

    // Придумываем новое значение
    float newVal = IsApprox(oldVal, 0.0f) ? 1.0f : 0.0f;

    // Записываем новое значение
    value = MmsValue_newFloat(newVal);
    IedConnection_writeObject(MMSConnEnv::getConn(), &err, objRef, IEC61850_FC_CF, *value);
    EXPECT_EQ(err, IED_ERROR_OK);

    // Считываем новое значение
    value = IedConnection_readObject(MMSConnEnv::getConn(), &err, objRef, IEC61850_FC_CF);
    EXPECT_EQ(err, IED_ERROR_OK);
    EXPECT_TRUE(value.isNotNull());
    EXPECT_EQ(value.getType(), MMS_FLOAT);
    float newReadValue = MmsValue_toFloat(*value);
    EXPECT_APPROX(newReadValue, newVal);

    if(!IsApprox(newReadValue, newVal)) {
        // Попробуем через паузу
        Sleep(1000);
        value = IedConnection_readObject(MMSConnEnv::getConn(), &err, objRef, IEC61850_FC_CF);
        EXPECT_EQ(err, IED_ERROR_OK);
        EXPECT_TRUE(value.isNotNull());
        EXPECT_EQ(value.getType(), MMS_FLOAT);
        EXPECT_APPROX(MmsValue_toFloat(*value), newVal);
    }
}

// Тест: sSrvN4 write Quality
TEST(MMSTests, WriteQuality) {
    IedClientError err;
    const char* objRef = "TEMPLATECTRL/LLN0.LocSta.q";

    MmsValWrap value;

    value = IedConnection_readObject(MMSConnEnv::getConn(), &err, objRef, IEC61850_FC_ST);
    EXPECT_TRUE(value.isNotNull());
    EXPECT_EQ(MmsValue_getType(*value), MMS_BIT_STRING);

    value = MmsValue_newBitString(1);
    IedConnection_writeObject(MMSConnEnv::getConn(), &err, objRef, IEC61850_FC_ST, *value);
    EXPECT_EQ(err, IED_ERROR_ACCESS_DENIED);
}

// Тест: sSrv8 alternate access - Simple
TEST(MMSTests, AlternateAccessSimple) {
    MmsValWrap value;
    MmsError mmsError;
    MmsConnection mmsCon = IedConnection_getMmsConnection(MMSConnEnv::getConn());

    value = MmsConnection_readVariableComponent(mmsCon, &mmsError,
        "TEMPLATECTRL", "LLN0", "ST");
    EXPECT_EQ(mmsError, MMS_ERROR_NONE);
    EXPECT_TRUE(value.isNotNull());
}

// Тест: sSrv8 alternate access - Complex
TEST(MMSTests, AlternateAccessComplex) {
    MmsValWrap value;
    MmsError mmsError;
    MmsConnection mmsCon = IedConnection_getMmsConnection(MMSConnEnv::getConn());

    value = MmsConnection_readVariableComponent(mmsCon, &mmsError,
        "TEMPLATECTRL", "LLN0", "DC$NamPlt$vendor");
    EXPECT_EQ(mmsError, MMS_ERROR_NONE);
    EXPECT_TRUE(value.isNotNull());
    EXPECT_EQ(value.getType(), MMS_VISIBLE_STRING);

    string vendor = MmsValue_toString(*value);
    EXPECT_EQ(vendor, "MTRA");
}

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);

    ::testing::AddGlobalTestEnvironment(new MMSConnEnv());

    return RUN_ALL_TESTS();
}
